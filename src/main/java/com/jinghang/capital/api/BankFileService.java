package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankFetchApplyDto;
import com.jinghang.capital.api.dto.bank.BankFetchResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 获取资方文件接口
 */
public interface BankFileService {

    /**
     * 获取资方文件
     *
     * @param fetchApply 请求
     * @return 内容
     */
    @PostMapping("fetch")
    RestResult<BankFetchResultDto> fetch(@RequestBody BankFetchApplyDto fetchApply);


}

package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.goods.GoodsDetailUploadDto;
import com.jinghang.capital.api.dto.goods.GoodsDetailUploadResultDto;
import com.jinghang.capital.api.dto.goods.GoodsLogisticsSynDto;
import com.jinghang.capital.api.dto.goods.GoodsReturnSynDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> gale
 * @Classname GoodsService
 * @Description 商品信息服务
 * @Date 2024/4/18 16:27
 */
public interface GoodsService {

    /**
     * 商品明细上传
     *
     * @param goodsDetailUploadDto
     * @return
     */
    @PostMapping("upload")
    RestResult<GoodsDetailUploadResultDto> goodsDetailUpload(@RequestBody GoodsDetailUploadDto goodsDetailUploadDto);

    /**
     * 商品物流信息
     */
    @PostMapping("logisticsSyn")
    RestResult<Void> logisticsSyn(@RequestBody GoodsLogisticsSynDto logisticsSynDto);

    /**
     * 商品退货信息
     */
    @PostMapping("returnSyn")
    RestResult<Void> returnSyn(@RequestBody GoodsReturnSynDto returnSynDto);


}

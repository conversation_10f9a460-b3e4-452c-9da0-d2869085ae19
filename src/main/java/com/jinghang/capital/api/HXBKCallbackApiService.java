package com.jinghang.capital.api;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * HXBK回调服务API接口
 * 用于Flow模块调用Capital模块的HXBK回调处理
 *
 * @Author: Lior
 * @CreateTime: 2025/7/10 22:30
 */
public interface HXBKCallbackApiService {

    /**
     * HXBK统一回调接口
     * 处理所有类型的HXBK回调请求
     *
     * @param requestBody 回调请求体
     * @return 回调响应
     */
    @PostMapping("/handle")
    String handle(@RequestBody String requestBody);

}

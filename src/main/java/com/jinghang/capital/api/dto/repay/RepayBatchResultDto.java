package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.StatusAble;

import java.util.List;

public class RepayBatchResultDto extends StatusAble {


    private String failMsg;

    private String outerRepayId;
    private String repayId;

    private String payOrderNo;

    private List<RepayBatchInfo> repayBatchInfo;
    private List<BankRepayRecordDto> bankRepayRecordList;

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public List<RepayBatchInfo> getRepayBatchInfo() {
        return repayBatchInfo;
    }

    public void setRepayBatchInfo(List<RepayBatchInfo> repayBatchInfo) {
        this.repayBatchInfo = repayBatchInfo;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public List<BankRepayRecordDto> getBankRepayRecordList() {
        return bankRepayRecordList;
    }

    public void setBankRepayRecordList(List<BankRepayRecordDto> bankRepayRecordList) {
        this.bankRepayRecordList = bankRepayRecordList;
    }
}

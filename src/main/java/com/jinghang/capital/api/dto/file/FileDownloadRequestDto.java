package com.jinghang.capital.api.dto.file;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/08
 */
public class FileDownloadRequestDto {
    /**
     * 内部借据号，Loan表的id
     */
    private String loanId;
    /**
     * 协议文件合作方提现单号, (对账文件不需要)
     */
    private String loanOrderId;


    private FileType type;

    /**
     * 文件名(下载对账文件时使用)
     */
    private String fileName;

    /**
     * 业务发生日期（下载对账文件使用）
     * 放款对账：放款日期
     * 还款对账： 实还日期
     * 代偿&回购对账： 应代偿日期
     * 代偿标记文件： 应代偿日期
     */
    private LocalDate fileDate;

    /**
     * 产品
     */
    private Product product;
    /**
     * 银行
     */
    private BankChannel bankChannel;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getLoanOrderId() {
        return loanOrderId;
    }

    public void setLoanOrderId(String loanOrderId) {
        this.loanOrderId = loanOrderId;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    @Override
    public String toString() {
        return "FileDownloadDto{loanOrderId='"
                + loanOrderId + '\''
                + ", loanId=" + '\''
                + loanId + '\''
                + ", type=" + type + ", fileName='" + fileName + '\''
                + ", product=" + product + ", bankChannel=" + bankChannel + '}';
    }
}

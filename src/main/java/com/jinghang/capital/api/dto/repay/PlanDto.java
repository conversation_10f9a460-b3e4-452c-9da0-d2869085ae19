package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.BankChannel;

import java.math.BigDecimal;
import java.util.List;

public class PlanDto {

    private BankChannel channel;

    private String sysLoanId;
    private String loanId;
    private BigDecimal loanAmt;
    private Integer periods;
    private List<PlanItemDto> planItems;


    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getSysLoanId() {
        return sysLoanId;
    }

    public void setSysLoanId(String sysLoanId) {
        this.sysLoanId = sysLoanId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public List<PlanItemDto> getPlanItems() {
        return planItems;
    }

    public void setPlanItems(List<PlanItemDto> planItems) {
        this.planItems = planItems;
    }
}

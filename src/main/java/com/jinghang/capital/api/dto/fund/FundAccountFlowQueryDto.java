package com.jinghang.capital.api.dto.fund;

import com.jinghang.capital.api.dto.BankChannel;

import java.time.LocalDate;

public class FundAccountFlowQueryDto {

    private BankChannel bankChannel;

    /**
     * 交易开始时间
     */
    private LocalDate tradeBeginTime;

    /**
     * 交易结束时间
     */
    private LocalDate tradeEndTime;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getTradeBeginTime() {
        return tradeBeginTime;
    }

    public void setTradeBeginTime(LocalDate tradeBeginTime) {
        this.tradeBeginTime = tradeBeginTime;
    }

    public LocalDate getTradeEndTime() {
        return tradeEndTime;
    }

    public void setTradeEndTime(LocalDate tradeEndTime) {
        this.tradeEndTime = tradeEndTime;
    }
}

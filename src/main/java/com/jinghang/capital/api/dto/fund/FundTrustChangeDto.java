package com.jinghang.capital.api.dto.fund;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.OperateTypeEnum;

import java.math.BigDecimal;

public class FundTrustChangeDto {


    /**
     * 资方渠道
     */
    private BankChannel bankChannel;

    /**
     * 信托编号
     */
    private String trustNo;

    /**
     * 变更类型
     */
    private OperateTypeEnum operateType;


    /**
     * 变更金额
     */
    private BigDecimal changeAmount;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getTrustNo() {
        return trustNo;
    }

    public void setTrustNo(String trustNo) {
        this.trustNo = trustNo;
    }

    public OperateTypeEnum getOperateType() {
        return operateType;
    }

    public void setOperateType(OperateTypeEnum operateType) {
        this.operateType = operateType;
    }

    public BigDecimal getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }
}

package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.StatusAble;

import java.time.LocalDateTime;

public class SubstituteApplyResultDto extends StatusAble {

    private LocalDateTime actSubstituteApplyTime;
    private String failMsg;

    public LocalDateTime getActSubstituteApplyTime() {
        return actSubstituteApplyTime;
    }

    public void setActSubstituteApplyTime(LocalDateTime actSubstituteApplyTime) {
        this.actSubstituteApplyTime = actSubstituteApplyTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }
}

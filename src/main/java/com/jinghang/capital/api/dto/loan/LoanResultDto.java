package com.jinghang.capital.api.dto.loan;

import com.jinghang.capital.api.dto.FundingModel;
import com.jinghang.capital.api.dto.StatusAble;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class LoanResultDto extends StatusAble {

    private String sysId;

    private String loanId;

    private String loanContractNo;

    /**
     * 放款金额
     */
    private BigDecimal loanAmt;

    /**
     * 期数
     */
    private Integer periods;

    /**
     * 放款时间
     */
    private LocalDateTime loanTime;

    private String failMsg;

    /**
     * 出资模式
     */
    private FundingModel fundingModel;


    /**
     * 出资相关字段
     */
    private FundInfo fundInfo;


    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getLoanContractNo() {
        return loanContractNo;
    }

    public void setLoanContractNo(String loanContractNo) {
        this.loanContractNo = loanContractNo;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public FundingModel getFundingModel() {
        return fundingModel;
    }

    public void setFundingModel(FundingModel fundingModel) {
        this.fundingModel = fundingModel;
    }

    public FundInfo getFundInfo() {
        return fundInfo;
    }

    public void setFundInfo(FundInfo fundInfo) {
        this.fundInfo = fundInfo;
    }
}

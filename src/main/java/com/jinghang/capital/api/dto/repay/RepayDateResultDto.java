package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.StatusAble;

import java.time.LocalDateTime;

public class RepayDateResultDto extends StatusAble {

    private LocalDateTime actTime;
    private String failMsg;

    public LocalDateTime getActTime() {
        return actTime;
    }

    public void setActTime(LocalDateTime actTime) {
        this.actTime = actTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }
}

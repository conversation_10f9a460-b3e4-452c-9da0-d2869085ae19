package com.jinghang.capital.api.dto;

public class RestResult<T> {
    public RestResult() {
    }

    private RestResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private String code;
    private String msg;
    private T data;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public static <T> RestResult<T> create(ResultCode code, String msg, T data) {
        return new RestResult<>(code.getCode(), msg, data);
    }

    public static <T> RestResult<T> create(ResultCode code, T data) {
        return create(code, code.getMsg(), data);
    }

    public static <T> RestResult<T> create(ResultCode code) {
        return create(code, code.getMsg(), null);
    }

    public static <T> RestResult<T> fail(ResultCode code, String msg) {
        return create(code, msg, null);
    }

    public static <T> RestResult<T> fail(ResultCode code) {
        return fail(code, code.getMsg());
    }

    public static <T> RestResult<T> success(T data) {
        return create(ResultCode.SUCCESS, data);
    }

    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}

package com.jinghang.capital.api.dto.repay;


import com.jinghang.capital.api.dto.StatusAble;

public class RepayReturnUploadResultDto extends StatusAble {
    private Integer uploadNumber;
    private String failMsg;

    public RepayReturnUploadResultDto() {
    }

    public String getFailMsg() {
        return this.failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public Integer getUploadNumber() {
        return uploadNumber;
    }

    public void setUploadNumber(Integer uploadNumber) {
        this.uploadNumber = uploadNumber;
    }
}

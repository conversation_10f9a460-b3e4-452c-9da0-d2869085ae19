package com.jinghang.capital.api.dto;

/**
 * 联系人关系
 */
public enum Relation {

    /**父母*/
    PARENTS("parents", "父母"),
    /**配偶*/
    SPOUSE("spouse", "配偶"),
    /**兄弟姐妹*/
    SIBLING("sibling", "兄弟姐妹"),
    /**朋友*/
    FRIEND("friend", "朋友"),
    /**同事*/
    COLLEAGUE("colleague", "同事"),
    /**子女*/
    CHILDREN("children", "子女"),
    /**亲戚*/
    RELATIVE("relative", "亲戚"),
    /**未知*/
    UNKNOWN("unknown", "未知");


    private final String code;

    private final String desc;

    Relation(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }

}

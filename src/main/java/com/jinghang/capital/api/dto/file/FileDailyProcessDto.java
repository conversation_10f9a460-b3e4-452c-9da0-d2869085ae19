package com.jinghang.capital.api.dto.file;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;

import java.time.LocalDate;

/**
 * 文件每日处理对象
 * (各个资方有不同处理)
 */
public class FileDailyProcessDto {
    /**
     * 需要处理日期
     */
    private LocalDate processDate;

    /**
     * 处理文件类型
     */
    private FileType type;


    /**
     * 银行
     */
    private BankChannel bankChannel;

    /**
     * 借据id
     */
    private String loanId;


    public LocalDate getProcessDate() {
        return processDate;
    }

    public void setProcessDate(LocalDate processDate) {
        this.processDate = processDate;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    @Override
    public String toString() {
        return "FileDailyProcessDto{"
            + "processDate=" + processDate
            + ", type=" + type
            + ", bankChannel=" + bankChannel
            + ", loanId=" + loanId
            + '}';
    }
}

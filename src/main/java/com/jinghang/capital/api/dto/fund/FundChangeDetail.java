package com.jinghang.capital.api.dto.fund;

import com.jinghang.capital.api.dto.OperateTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDate;

public class FundChangeDetail {

    private LocalDate changeDate;

    private OperateTypeEnum operateType;

    private BigDecimal changeAmount;


    private BigDecimal creditAmount;

    public LocalDate getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(LocalDate changeDate) {
        this.changeDate = changeDate;
    }

    public OperateTypeEnum getOperateType() {
        return operateType;
    }

    public void setOperateType(OperateTypeEnum operateType) {
        this.operateType = operateType;
    }

    public BigDecimal getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }
}

package com.jinghang.capital.api.dto.repay;

import java.math.BigDecimal;

public class ActiveClaimApplyDto {
    private String outerLoanId;
    private String loanId;
    private String outerClaimId;
    private Integer period;
    private RepayPurpose claimPurpose;
    private BigDecimal amount;
    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal penalty;
    private BigDecimal guaranteeFee;
    private BigDecimal consultFee;
    private BigDecimal breachFee;

    public ActiveClaimApplyDto() {
    }

    public String getOuterLoanId() {
        return this.outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterClaimId() {
        return this.outerClaimId;
    }

    public void setOuterClaimId(String outerClaimId) {
        this.outerClaimId = outerClaimId;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getClaimPurpose() {
        return this.claimPurpose;
    }

    public void setClaimPurpose(RepayPurpose claimPurpose) {
        this.claimPurpose = claimPurpose;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return this.principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return this.interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return this.penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuaranteeFee() {
        return this.guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getConsultFee() {
        return this.consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachFee() {
        return this.breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }
}

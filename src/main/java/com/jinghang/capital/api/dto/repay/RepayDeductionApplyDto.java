package com.jinghang.capital.api.dto.repay;


import java.math.BigDecimal;
import java.util.List;

public class RepayDeductionApplyDto {


    /**
     * 还款id
     */
    private String outerRepayId;

    /**
     * 扣款协议号
     */
    private String agreementNo;

    /**
     * 还款银行编码
     */
    private String repayBankCode;

    /**
     * 还款卡号
     */
    private String repayAcctNo;

    /**
     * 还款方用户名
     */
    private String repayRelUser;

    /**
     * 还款方手机号
     */
    private String repayRelPhone;

    /**
     * 还款方身份证号
     */
    private String repayRelCard;

    /**
     * 还款模式
     */
    private RepayPurpose repayPurpose;

    /**
     * 还款方式
     */
    private RepayMode repayMode;

    /**
     * 还款类型
     */
    private RepayType repayType;

    /**
     * 还款类别
     */
    private RepayCategory repayCategory;

    /**
     * 减免金额
     */
    private BigDecimal totalReduceAmt;

    /**
     * 还款数据列表
     */
    private List<RepayPlanItemDto> applyList;


    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayAcctNo() {
        return repayAcctNo;
    }

    public void setRepayAcctNo(String repayAcctNo) {
        this.repayAcctNo = repayAcctNo;
    }

    public String getRepayRelUser() {
        return repayRelUser;
    }

    public void setRepayRelUser(String repayRelUser) {
        this.repayRelUser = repayRelUser;
    }

    public String getRepayRelPhone() {
        return repayRelPhone;
    }

    public void setRepayRelPhone(String repayRelPhone) {
        this.repayRelPhone = repayRelPhone;
    }

    public String getRepayRelCard() {
        return repayRelCard;
    }

    public void setRepayRelCard(String repayRelCard) {
        this.repayRelCard = repayRelCard;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }

    public List<RepayPlanItemDto> getApplyList() {
        return applyList;
    }

    public void setApplyList(List<RepayPlanItemDto> applyList) {
        this.applyList = applyList;
    }

    public BigDecimal getTotalReduceAmt() {
        return totalReduceAmt;
    }

    public void setTotalReduceAmt(BigDecimal totalReduceAmt) {
        this.totalReduceAmt = totalReduceAmt;
    }
}

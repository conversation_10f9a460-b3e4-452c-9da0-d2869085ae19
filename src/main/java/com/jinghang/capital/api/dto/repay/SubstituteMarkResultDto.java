package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.StatusAble;

import java.time.LocalDateTime;

public class SubstituteMarkResultDto extends StatusAble {

    private LocalDateTime actSubstituteMarkTime;
    private String failMsg;

    public LocalDateTime getActSubstituteMarkTime() {
        return actSubstituteMarkTime;
    }

    public void setActSubstituteMarkTime(LocalDateTime actSubstituteMarkTime) {
        this.actSubstituteMarkTime = actSubstituteMarkTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }
}

package com.jinghang.capital.api.dto.fund;

import com.jinghang.capital.api.dto.LoanTag;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class FundDetail {

    /**
     * 流水号
     */
    private String tranNo;

    /**
     * 交易日
     * 格式：YYYYMMDD，不可为
     */
    private LocalDate faDate;

    /**
     * 交易时间
     * 日期格式:yyyyMMddhhmmss
     */
    private LocalDateTime tradeTime;

    /**
     * 账户余额，单位：元
     */
    private BigDecimal balanceAmt;

    /**
     * 交易金额，单位：元
     */
    private BigDecimal tradeAmt;

    /**
     * 借贷标记
     * -1:支出
     * 1:收入
     */
    private LoanTag loanTag;

    /**
     * 对方帐号
     */
    private String bankAccNo;

    /**
     * 对方账户名称
     */
    private String bankAccName;

    /**
     * 摘要
     */
    private String note;

    /**
     * 备注
     */
    private String remark;

    public String getTranNo() {
        return tranNo;
    }

    public void setTranNo(String tranNo) {
        this.tranNo = tranNo;
    }

    public LocalDate getFaDate() {
        return faDate;
    }

    public void setFaDate(LocalDate faDate) {
        this.faDate = faDate;
    }

    public LocalDateTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalDateTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public LoanTag getLoanTag() {
        return loanTag;
    }

    public void setLoanTag(LoanTag loanTag) {
        this.loanTag = loanTag;
    }

    public String getBankAccNo() {
        return bankAccNo;
    }

    public void setBankAccNo(String bankAccNo) {
        this.bankAccNo = bankAccNo;
    }

    public String getBankAccName() {
        return bankAccName;
    }

    public void setBankAccName(String bankAccName) {
        this.bankAccName = bankAccName;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

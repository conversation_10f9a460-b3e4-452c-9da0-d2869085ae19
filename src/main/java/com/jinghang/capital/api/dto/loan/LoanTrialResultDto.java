package com.jinghang.capital.api.dto.loan;

import java.math.BigDecimal;
import java.util.List;

public class LoanTrialResultDto {

    /**
     * 本金总额
     */
    private BigDecimal principalTotal;

    /**
     * 利息总额
     */
    private BigDecimal interestTotal;

    /**
     * 还款计划试算明细
     */
    private List<RepayPlanTrial> repayPlanTrials;

    public BigDecimal getPrincipalTotal() {
        return principalTotal;
    }

    public void setPrincipalTotal(BigDecimal principalTotal) {
        this.principalTotal = principalTotal;
    }

    public BigDecimal getInterestTotal() {
        return interestTotal;
    }

    public void setInterestTotal(BigDecimal interestTotal) {
        this.interestTotal = interestTotal;
    }

    public List<RepayPlanTrial> getRepayPlanTrials() {
        return repayPlanTrials;
    }

    public void setRepayPlanTrials(List<RepayPlanTrial> repayPlanTrials) {
        this.repayPlanTrials = repayPlanTrials;
    }
}

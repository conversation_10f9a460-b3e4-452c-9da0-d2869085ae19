package com.jinghang.capital.api.dto.credit;

import com.jinghang.capital.api.dto.ProtocolChannel;

public class BankCardInfoDto {

    /**
     * 户名
     */
    private String cardName;
    /**
     * 银行卡号
     */
    private String cardNo;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 绑卡渠道
     */
    private ProtocolChannel payChannel;

    /**
     * 扣款协议
     */
    private String protocolNo;

    private String protocolUserNo;

    public String getProtocolUserNo() {
        return protocolUserNo;
    }

    public void setProtocolUserNo(String protocolUserNo) {
        this.protocolUserNo = protocolUserNo;
    }

    public ProtocolChannel getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(ProtocolChannel payChannel) {
        this.payChannel = payChannel;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

}

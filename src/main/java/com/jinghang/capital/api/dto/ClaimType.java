package com.jinghang.capital.api.dto;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/6/9
 */
public enum ClaimType {
    SINGLE("CURRENT", "单期"),
    ENTIRE("CLEAR", "整笔");

    private String code;
    private String desc;


    ClaimType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ClaimType getClaimType(String code) {
        return Arrays.stream(values()).filter(c -> code.equals(c.getCode())).findFirst().orElse(null);
    }
}

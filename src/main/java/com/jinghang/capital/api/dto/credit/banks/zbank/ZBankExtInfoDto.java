package com.jinghang.capital.api.dto.credit.banks.zbank;

public class ZBankExtInfoDto {

    /**
     * 客户类型
     * 0310-个人客户;
     * 0320-个体经营户;
     * 0330-小微企业主;
     */
    private String cstTp;
    /**
     * 户籍地址-省市区
     */
    private String addCnty;
    /**
     * 户籍地址-详细
     */
    private String ntvAddDtls;
    /**
     * 居住状况
     * 1 自置
     * 2 按揭
     * 3 亲属楼宇
     * 4 集体宿舍
     * 5 租房
     * 6 共有住宅
     * 7 其他
     * 9 未知
     */
    private String lvngCndtn;
    /**
     * 居住地址-省市区
     */
    private String prmtAdr;
    /**
     * 居住地址-详细
     */
    private String addDtls;
    /**
     * 通讯地址-省市区
     */
    private String cntArCd;
    /**
     * 通讯地址-详细
     */
    private String cntcAddr;
    /**
     * 职业
     * 0-国家机关、党群组织、企业、事业单位负责人
     * 1-专业技术人员
     * 3-办事人员和有关人员
     * 4-商业、服务业人员
     * 5-农、林、牧、渔、水利业生产人员
     * 6-生产、运输设备操作人员及有关人员
     * X-军人
     */
    private String ocptn;
    /**
     * 职务
     * 1-高级领导（行政级别局级及局级以上领导或大公司高级管理人员）；
     * 2-中级领导（行政级别局级以下领导或大公司中级管理人员）；
     * 3-一般员工；
     * 4-其他；
     */
    private String posCd;
    /**
     * 职称
     * 0 无
     * 1 高级
     * 2 中级
     * 3 初级
     * 9 未知
     */
    private String thTtl;
    /**
     * 工作单位性质
     * 10-行政单位；
     * 21-科研设计单位；
     * 22-高等学校；
     * 23-其它教学单位；
     * 24-医疗卫生单位；
     * 29-其它事业单位；
     * 31-国有企业；
     * 32-三资企业；
     * 33-民营企业；
     * 39-其它企业；
     * 40-部队；
     * 99-其它
     */
    private String coKnd;
    /**
     * 单位所属行业
     */
    private String ntKnd;
    /**
     * 单位地址-省市区
     */
    private String provCd;
    /**
     * 单位地址-详细
     */
    private String coAdr;
    /**
     * 是否农户
     * 1-是；2-否
     */
    private String whthrFrmrs;
    /**
     * 是否农户贷款
     * 1-是；2-否
     */
    private String isNonLon;
    /**
     * 是否涉农贷款
     * 1-是；2-否
     */
    private String isNonLod;
    /**
     * GPS 经度
     */
    private String lgtd;
    /**
     * GPS 纬度
     */
    private String ltud;
    /**
     * GPS 地址
     */
    private String gpsAddr;
    /**
     * IP 网络
     */
    private String ipNet;
    /**
     * IP 网络对应的地址
     */
    private String ipNetAdd;
    /**
     * LBS（行程信息）
     */
    private String lbs;
    /**
     * 评分
     */
    private String score;

    public String getCstTp() {
        return cstTp;
    }

    public void setCstTp(String cstTp) {
        this.cstTp = cstTp;
    }

    public String getAddCnty() {
        return addCnty;
    }

    public void setAddCnty(String addCnty) {
        this.addCnty = addCnty;
    }

    public String getNtvAddDtls() {
        return ntvAddDtls;
    }

    public void setNtvAddDtls(String ntvAddDtls) {
        this.ntvAddDtls = ntvAddDtls;
    }

    public String getLvngCndtn() {
        return lvngCndtn;
    }

    public void setLvngCndtn(String lvngCndtn) {
        this.lvngCndtn = lvngCndtn;
    }

    public String getPrmtAdr() {
        return prmtAdr;
    }

    public void setPrmtAdr(String prmtAdr) {
        this.prmtAdr = prmtAdr;
    }

    public String getAddDtls() {
        return addDtls;
    }

    public void setAddDtls(String addDtls) {
        this.addDtls = addDtls;
    }

    public String getCntArCd() {
        return cntArCd;
    }

    public void setCntArCd(String cntArCd) {
        this.cntArCd = cntArCd;
    }

    public String getCntcAddr() {
        return cntcAddr;
    }

    public void setCntcAddr(String cntcAddr) {
        this.cntcAddr = cntcAddr;
    }

    public String getOcptn() {
        return ocptn;
    }

    public void setOcptn(String ocptn) {
        this.ocptn = ocptn;
    }

    public String getPosCd() {
        return posCd;
    }

    public void setPosCd(String posCd) {
        this.posCd = posCd;
    }

    public String getThTtl() {
        return thTtl;
    }

    public void setThTtl(String thTtl) {
        this.thTtl = thTtl;
    }

    public String getCoKnd() {
        return coKnd;
    }

    public void setCoKnd(String coKnd) {
        this.coKnd = coKnd;
    }

    public String getNtKnd() {
        return ntKnd;
    }

    public void setNtKnd(String ntKnd) {
        this.ntKnd = ntKnd;
    }

    public String getProvCd() {
        return provCd;
    }

    public void setProvCd(String provCd) {
        this.provCd = provCd;
    }

    public String getCoAdr() {
        return coAdr;
    }

    public void setCoAdr(String coAdr) {
        this.coAdr = coAdr;
    }

    public String getWhthrFrmrs() {
        return whthrFrmrs;
    }

    public void setWhthrFrmrs(String whthrFrmrs) {
        this.whthrFrmrs = whthrFrmrs;
    }

    public String getIsNonLon() {
        return isNonLon;
    }

    public void setIsNonLon(String isNonLon) {
        this.isNonLon = isNonLon;
    }

    public String getIsNonLod() {
        return isNonLod;
    }

    public void setIsNonLod(String isNonLod) {
        this.isNonLod = isNonLod;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLtud() {
        return ltud;
    }

    public void setLtud(String ltud) {
        this.ltud = ltud;
    }

    public String getGpsAddr() {
        return gpsAddr;
    }

    public void setGpsAddr(String gpsAddr) {
        this.gpsAddr = gpsAddr;
    }

    public String getIpNet() {
        return ipNet;
    }

    public void setIpNet(String ipNet) {
        this.ipNet = ipNet;
    }

    public String getIpNetAdd() {
        return ipNetAdd;
    }

    public void setIpNetAdd(String ipNetAdd) {
        this.ipNetAdd = ipNetAdd;
    }

    public String getLbs() {
        return lbs;
    }

    public void setLbs(String lbs) {
        this.lbs = lbs;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }
}

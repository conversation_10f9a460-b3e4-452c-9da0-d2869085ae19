package com.jinghang.capital.api.dto.recc;

import com.jinghang.capital.api.dto.StatusAble;

import java.time.LocalDateTime;

public class ReccResultDto extends StatusAble {

    private LocalDateTime actReccTime;
    private String failMsg;

    public LocalDateTime getActReccTime() {
        return actReccTime;
    }

    public void setActReccTime(LocalDateTime actReccTime) {
        this.actReccTime = actReccTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }
}

package com.jinghang.capital.api.dto.credit;

import com.jinghang.capital.api.dto.StatusAble;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class CreditResultDto extends StatusAble {

    private String sysId;
    private String creditId;
    private String creditContractNo;
    private BigDecimal creditAmt;
    /**
     * 资方授信到期时间
     */
    private LocalDateTime capExpireTime;

    /**
     * 授信结果金额
     */
    private BigDecimal creditResultAmt;

    private Integer period;

    private LocalDateTime creditTime;

    private String failMsg;

    public String getSysId() {
        return sysId;
    }

    public CreditResultDto setSysId(String sysId) {
        this.sysId = sysId;
        return this;
    }

    public LocalDateTime getCapExpireTime() {
        return capExpireTime;
    }

    public void setCapExpireTime(LocalDateTime capExpireTime) {
        this.capExpireTime = capExpireTime;
    }

    public String getCreditId() {
        return creditId;
    }

    public CreditResultDto setCreditId(String creditId) {
        this.creditId = creditId;
        return this;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public CreditResultDto setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
        return this;
    }

    public Integer getPeriod() {
        return period;
    }

    public CreditResultDto setPeriod(Integer period) {
        this.period = period;
        return this;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public CreditResultDto setFailMsg(String failMsg) {
        this.failMsg = failMsg;
        return this;
    }

    public LocalDateTime getCreditTime() {
        return creditTime;
    }

    public CreditResultDto setCreditTime(LocalDateTime creditTime) {
        this.creditTime = creditTime;
        return this;
    }

    public BigDecimal getCreditResultAmt() {
        return creditResultAmt;
    }

    public void setCreditResultAmt(BigDecimal creditResultAmt) {
        this.creditResultAmt = creditResultAmt;
    }
}

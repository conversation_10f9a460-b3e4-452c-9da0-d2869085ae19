package com.jinghang.capital.api.dto.file;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
public class FileUploadDto {
    /**
     * 协议文件合作方提现单号, (对账文件不需要)
     */
    private String loanOrderId;


    private FileType type;

    /**
     * 文件名(下载对账文件时使用)
     */
    private String fileName;


    private String bucketName;

    private String fileKey;


    /**
     * 产品
     */
    private Product product;
    /**
     * 银行
     */
    private BankChannel bankChannel;

    public String getLoanOrderId() {
        return loanOrderId;
    }

    public void setLoanOrderId(String loanOrderId) {
        this.loanOrderId = loanOrderId;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }


    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    @Override
    public String toString() {
        return "FileDownloadDto{loanOrderId='"
                + loanOrderId + '\''
                + ", type=" + type + ", fileName='" + fileName + '\''
                + ", product=" + product + ", bankChannel=" + bankChannel + '}';
    }
}

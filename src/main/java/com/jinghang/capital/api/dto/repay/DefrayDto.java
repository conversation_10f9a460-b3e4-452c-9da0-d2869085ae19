package com.jinghang.capital.api.dto.repay;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.DefrayType;

import java.time.LocalDate;

public class DefrayDto {

    private BankChannel bankChannel;

    private LocalDate recordDay;

    private DefrayType defrayType;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getRecordDay() {
        return recordDay;
    }

    public void setRecordDay(LocalDate recordDay) {
        this.recordDay = recordDay;
    }

    public DefrayType getDefrayType() {
        return defrayType;
    }

    public void setDefrayType(DefrayType defrayType) {
        this.defrayType = defrayType;
    }
}

package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.StatusAble;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class RepayResultDto extends StatusAble {

    private LocalDateTime actRepayTime;
    private String failMsg;

    private String outerRepayId;

    private String repayId;

    /**
     *  资方还款流水号
     */
    private String bankSeq;


    /**
     * 代扣流水号
     */
    private String payOrderNo;

    /**
     * 对资罚息金额
     */
    private BigDecimal bankPenaltyAmt;

    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public BigDecimal getBankPenaltyAmt() {
        return bankPenaltyAmt;
    }

    public void setBankPenaltyAmt(BigDecimal bankPenaltyAmt) {
        this.bankPenaltyAmt = bankPenaltyAmt;
    }

    public String getBankSeq() {
        return bankSeq;
    }

    public void setBankSeq(String bankSeq) {
        this.bankSeq = bankSeq;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }
}

package com.jinghang.capital.api.dto.goods;

import com.jinghang.capital.api.dto.BankChannel;

import java.time.LocalDate;
import java.util.List;

public class GoodsDetailUploadDto {

    /**
     * 资方
     */
    private BankChannel bankChannel;
    /**
     * 上传日期
     */
    private LocalDate uploadDate;
    /**
     * 推送的数据 List<Json> 每条数据转为Json
     */
    private List<String> dataList;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(LocalDate uploadDate) {
        this.uploadDate = uploadDate;
    }

    public List<String> getDataList() {
        return dataList;
    }

    public void setDataList(List<String> dataList) {
        this.dataList = dataList;
    }


}

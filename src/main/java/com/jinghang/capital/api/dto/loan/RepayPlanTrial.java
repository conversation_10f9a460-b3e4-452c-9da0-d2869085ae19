package com.jinghang.capital.api.dto.loan;

import java.math.BigDecimal;
import java.time.LocalDate;

public class RepayPlanTrial {

    /**
     * 期数
     */
    private Integer period;

    /**
     * 应还时间
     */
    private LocalDate repayDate;

    /**
     * 应还本金
     */
    private BigDecimal principalAmt;

    /**
     * 应还利息
     */
    private BigDecimal interestAmt;

    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }
}

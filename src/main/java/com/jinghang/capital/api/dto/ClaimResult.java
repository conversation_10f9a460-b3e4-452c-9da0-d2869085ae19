package com.jinghang.capital.api.dto;

/**
 * <AUTHOR> gale
 * @Classname ClaimResult
 * @Description TODO
 * @Date 2025/5/9 14:12
 */
public enum ClaimResult {
    PROCESSING(0, "代偿处理中"),
    SUCCESS(1, "代偿成功") {
        public boolean isFinal() {
            return true;
        }
    },
    FAIL(2, "代偿失败") {
        public boolean isFinal() {
            return true;
        }
    },
    REPEATE(3, "重复代偿") {
        public boolean isFinal() {
            return true;
        }
    },
    NON_EXISTENT(9, "代偿申请不存在");

    private final Integer code;
    private final String desc;

    ClaimResult(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public boolean isFinal() {
        return false;
    }
}

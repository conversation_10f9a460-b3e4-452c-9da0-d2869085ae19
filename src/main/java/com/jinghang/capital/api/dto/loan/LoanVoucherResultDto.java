package com.jinghang.capital.api.dto.loan;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName LoanVoucherResultDto
 * <AUTHOR>
 * @Description 借款记录，用于结清证明下载
 * @Date 2024/1/9 13:58
 * @Version v1.0
 **/
public class LoanVoucherResultDto {
    private String id;
    private String custName;
    private String custMobile;
    private String custCertNo;
    private BigDecimal loanAmt;
    private Integer periods;
    /**
     * 放款时间
     */
    private LocalDateTime loanTime;
    /**
     * 资方渠道
     */
    private String channel;
    /**
     * 是否结清
     */
    private String isClear;
    /**
     * 结清时间
     */
    private String clearTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustMobile() {
        return custMobile;
    }

    public void setCustMobile(String custMobile) {
        this.custMobile = custMobile;
    }

    public String getCustCertNo() {
        return custCertNo;
    }

    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getIsClear() {
        return isClear;
    }

    public void setIsClear(String isClear) {
        this.isClear = isClear;
    }

    public String getClearTime() {
        return clearTime;
    }

    public void setClearTime(String clearTime) {
        this.clearTime = clearTime;
    }
}

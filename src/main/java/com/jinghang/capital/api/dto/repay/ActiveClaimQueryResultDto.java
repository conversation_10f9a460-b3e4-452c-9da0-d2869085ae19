package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.ClaimResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class ActiveClaimQueryResultDto {
    private ClaimResult claimResult;
    private String msg;
    private String outerClaimId;
    private String claimId;
    private BigDecimal amount;
    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal penalty;
    private BigDecimal guaranteeFee;
    private BigDecimal consultFee;
    private BigDecimal breachFee;
    private LocalDateTime actClaimTime;

    public ActiveClaimQueryResultDto() {
    }

    public ClaimResult getClaimResult() {
        return this.claimResult;
    }

    public void setClaimResult(ClaimResult claimResult) {
        this.claimResult = claimResult;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOuterClaimId() {
        return this.outerClaimId;
    }

    public void setOuterClaimId(String outerClaimId) {
        this.outerClaimId = outerClaimId;
    }

    public String getClaimId() {
        return this.claimId;
    }

    public void setClaimId(String claimId) {
        this.claimId = claimId;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return this.principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return this.interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return this.penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuaranteeFee() {
        return this.guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getConsultFee() {
        return this.consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachFee() {
        return this.breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    public LocalDateTime getActClaimTime() {
        return this.actClaimTime;
    }

    public void setActClaimTime(LocalDateTime actClaimTime) {
        this.actClaimTime = actClaimTime;
    }
}

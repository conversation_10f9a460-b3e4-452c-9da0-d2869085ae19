package com.jinghang.capital.api.dto.loan;

import com.jinghang.capital.api.dto.PageParam;
import com.jinghang.capital.api.dto.ProcessStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class LoanLimitResultDto extends PageParam {

    /**
     * 授信额度
     */
    private BigDecimal creditAmount;
    /**
     * 可用额度
     */
    private BigDecimal availableAmount;
    /**
     * 额度生效日期
     */
    private String beginDate;
    /**
     * 额度失效日期
     */
    private String endDate;
    /**
     * 额度利率
     */
    private BigDecimal rate;
    /**
     * 额度更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 状态
     */
    private ProcessStatus status;

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}

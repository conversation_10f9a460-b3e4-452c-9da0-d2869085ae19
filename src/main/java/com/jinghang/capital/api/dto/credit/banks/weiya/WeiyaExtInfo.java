package com.jinghang.capital.api.dto.credit.banks.weiya;

import java.math.BigDecimal;

public class WeiyaExtInfo {
    /**
     * 纬雅第一次的授信id,需要设置为失效
     */
    private String firstCreditId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品二级分类Id
     */
    private Integer categoryTwoId;
    /**
     * 商品品牌名称
     */
    private String brandName;
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    /**
     * 商品数量
     */
    private Integer productNumber;
    /**
     * sku编码名称
     */
    private String skuName;
    /**
     * sku编码价格
     */
    private String skuPrice;
    /**
     * 收货信息姓名
     */
    private String buyerName;
    /**
     * 收货信息买家电话
     */
    private String buyerPhone;
    /**
     * 收货信息买家地址
     */
    private String buyerAddress;
    /**
     * 买家留言
     */
    private String buyerMessage;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getCategoryTwoId() {
        return categoryTwoId;
    }

    public void setCategoryTwoId(Integer categoryTwoId) {
        this.categoryTwoId = categoryTwoId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public BigDecimal getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(BigDecimal productPrice) {
        this.productPrice = productPrice;
    }

    public Integer getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(Integer productNumber) {
        this.productNumber = productNumber;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(String skuPrice) {
        this.skuPrice = skuPrice;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerPhone() {
        return buyerPhone;
    }

    public void setBuyerPhone(String buyerPhone) {
        this.buyerPhone = buyerPhone;
    }

    public String getBuyerAddress() {
        return buyerAddress;
    }

    public void setBuyerAddress(String buyerAddress) {
        this.buyerAddress = buyerAddress;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getFirstCreditId() {
        return firstCreditId;
    }

    public void setFirstCreditId(String firstCreditId) {
        this.firstCreditId = firstCreditId;
    }
}

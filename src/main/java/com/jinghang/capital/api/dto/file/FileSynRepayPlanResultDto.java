package com.jinghang.capital.api.dto.file;

import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;

import java.time.LocalDate;
public class FileSynRepayPlanResultDto {

    /**
     * 文件日期
     */
    private LocalDate fileDate;

    /**
     * 文件类型
     */
    private FileType fileType;

    /**
     * 文件名
     */
    private String fileName;


    /**
     * oss_bucket
     */
    private String ossBucket;


    /**
     * oss_key
     */
    private String ossKey;

    /**
     * 文件状态
     */
    private ProcessStatus fileStatus;

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public ProcessStatus getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(ProcessStatus fileStatus) {
        this.fileStatus = fileStatus;
    }
}

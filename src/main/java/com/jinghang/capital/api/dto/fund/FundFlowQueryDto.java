package com.jinghang.capital.api.dto.fund;

import com.jinghang.capital.api.dto.BankChannel;

import java.time.LocalDate;

public class FundFlowQueryDto {

    /**
     * 资方渠道
     */
    private BankChannel bankChannel;

    /**
     * 信托编号
     */
    private String trustNo;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 当前页
     */
    private Integer pageIndex;


    /**
     * 每页多少条
     */
    private Integer pageSize;



    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getTrustNo() {
        return trustNo;
    }

    public void setTrustNo(String trustNo) {
        this.trustNo = trustNo;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}

package com.jinghang.capital.api.dto.plan;

import com.jinghang.capital.api.dto.BankChannel;

import java.time.LocalDate;

public class PlanOverdueDto {

    /**
     * 资方渠道
     */
    private BankChannel bankChannel;

    private LocalDate handleDay;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getHandleDay() {
        return handleDay;
    }

    public void setHandleDay(LocalDate handleDay) {
        this.handleDay = handleDay;
    }
}

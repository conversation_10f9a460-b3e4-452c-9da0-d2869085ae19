package com.jinghang.capital.api.dto.plan;

import com.jinghang.capital.api.dto.RepayStatus;

import java.math.BigDecimal;
import java.time.LocalDate;

public class BankPlanItemDto {

    /**
     * 借款ID
     */
    private String loanId;

    /**
     * 期次
     */
    private Integer period;
    /**
     * 应还时间
     */
    private LocalDate repayDate;
    /**
     * 应还总金额
     */
    private BigDecimal totalAmt;
    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    private BigDecimal interestAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 应还违约金
     */
    private BigDecimal breachAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;
    /**
     * 咨询费用
     */
    private BigDecimal consultAmt;

    /**
     * 对客还款状态
     */
    private RepayStatus custRepayStatus;

    /**
     * 对资还款状态
     */
    private RepayStatus bankRepayStatus;

    /**
     * 是否标记代偿(Y表示已标记代偿)
     */
    private String isMarkClaim;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    public RepayStatus getCustRepayStatus() {
        return custRepayStatus;
    }

    public void setCustRepayStatus(RepayStatus custRepayStatus) {
        this.custRepayStatus = custRepayStatus;
    }

    public RepayStatus getBankRepayStatus() {
        return bankRepayStatus;
    }

    public void setBankRepayStatus(RepayStatus bankRepayStatus) {
        this.bankRepayStatus = bankRepayStatus;
    }

    public String getIsMarkClaim() {
        return isMarkClaim;
    }

    public void setIsMarkClaim(String isMarkClaim) {
        this.isMarkClaim = isMarkClaim;
    }
}

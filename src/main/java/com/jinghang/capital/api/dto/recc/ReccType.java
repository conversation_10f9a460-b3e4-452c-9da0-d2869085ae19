package com.jinghang.capital.api.dto.recc;

/**
 * 对账类型
 */
public enum ReccType {
    /** 放款 */
    LOAN,
    /** 还款 */
    REPAY,
    /** 放款后还款计划*/
    PLAN,
    /** 还款计划 */
    REPAY_PLAN,
    /** 代扣 */
    DEDUCT,
    /** 逾期 */
    OVERDUE,
    /** 代偿 */
    COMPENSATION,
    /** 预代偿 */
    PRE_COMPENSATION,
    /** 回购 */
    REPURCHASE,
    /** 预回购 */
    PRE_REPURCHASE,
    /** 代偿后 */
    CLAIM_AFTER,
    /** 对客放款 */
    CUST_LOAN,
    /** 对客放款后初始化还款计划*/
    CUST_LOAN_PLAN,
    /** 对客真实还款 */
    CUST_REPAY,
    /**
     * 对客含咨询费还款记录
     */
    CUST_REPAY_CONFEE,
    /** 对客营销减免 */
    DISCOUNT,
    /** 账单 */
    BILL,
    /** 挂帐信息 */
    SUSPENSE,
    /**
     * 对客日终借据文件
     */
    CUST_DAILY_LOAN,
    /**
     * 对客日终还款计划文件
     */
    CUST_DAILY_LOAN_PLAN,
    /**
     * 对客日终担保费文件
     */
    CUST_DAILY_GUARANTEE,
    /**
     * 对客日终担保费计划文件
     */
    CUST_DAILY_GUARANTEE_PLAN,
    /**
     * 贷款信息数据文件
     */
    LOAN_INFO_TABLE_FILE,

    /**
     * 还款记录数据文件
     */
    REPAY_RECORD_TABLE_FILE,

    /**
     * 还款计划数据文件
     */
    REPAY_PLAN_TABLE_FILE,

    /**
     * 账单数据文件
     */
    RECONCILE_FILE;
}

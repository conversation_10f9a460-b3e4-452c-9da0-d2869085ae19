package com.jinghang.capital.api.dto.repay;

import com.jinghang.capital.api.dto.BankChannel;

import java.math.BigDecimal;
import java.util.List;

public class BatchTrailDto {

    /**
     * 资方渠道
     */
    private BankChannel channel;

    /**
     * 还款模式
     */
    private RepayPurpose repayPurpose;
    /**
     * 还款类型
     */
    private RepayType repayType;
    /**
     * 减免总额
     */
    private BigDecimal totalReduceAmt;

    /**
     * 还款试算列表
     */
    private List<BatchTrailItemDto> trailItems;

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public List<BatchTrailItemDto> getTrailItems() {
        return trailItems;
    }

    public void setTrailItems(List<BatchTrailItemDto> trailItems) {
        this.trailItems = trailItems;
    }

    public BigDecimal getTotalReduceAmt() {
        return totalReduceAmt;
    }

    public void setTotalReduceAmt(BigDecimal totalReduceAmt) {
        this.totalReduceAmt = totalReduceAmt;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }
}

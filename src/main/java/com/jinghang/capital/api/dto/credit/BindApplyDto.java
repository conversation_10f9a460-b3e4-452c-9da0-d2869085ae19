package com.jinghang.capital.api.dto.credit;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.BindSignMode;
import com.jinghang.capital.api.dto.ProtocolChannel;

/**
 * 绑卡申请dto
 */
public class BindApplyDto {

    /**
     * 银行
     */
    private BankChannel bankChannel;

    /**
     * 外部授信id
     */
    private String sysCreditId;

    /**
     * 外部放款id
     */
    private String sysLoanId;

    /**
     * 持卡人姓名
     */
    private String cardName;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 手机号码
     */
    private String phone;


    private String bankName;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 签约模式
     */
    private BindSignMode bindSignMode;

    /**
     * 签约渠道
     */
    private ProtocolChannel protocolChannel;

    /**
     * 签约协议号
     */
    private String agreementNo;

    public BindSignMode getBindSignMode() {
        return bindSignMode;
    }

    public void setBindSignMode(BindSignMode bindSignMode) {
        this.bindSignMode = bindSignMode;
    }

    public ProtocolChannel getProtocolChannel() {
        return protocolChannel;
    }

    public void setProtocolChannel(ProtocolChannel protocolChannel) {
        this.protocolChannel = protocolChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getSysCreditId() {
        return sysCreditId;
    }

    public void setSysCreditId(String sysCreditId) {
        this.sysCreditId = sysCreditId;
    }

    public String getSysLoanId() {
        return sysLoanId;
    }

    public void setSysLoanId(String sysLoanId) {
        this.sysLoanId = sysLoanId;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }
}

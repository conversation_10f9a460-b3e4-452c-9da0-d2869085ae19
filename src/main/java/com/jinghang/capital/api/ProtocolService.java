package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.protocol.ProtocolSyncDto;
import com.jinghang.capital.api.dto.protocol.ProtocolSyncRltDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ProtocolService {

    @PostMapping("sync")
    RestResult<ProtocolSyncRltDto> sync(@RequestBody ProtocolSyncDto syncResult);
}

package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.jinghang.capital.api.dto.credit.BindConfirmDto;
import com.jinghang.capital.api.dto.credit.BindResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 绑卡服务
 */
public interface BindService {

    @PostMapping("apply")
    RestResult<BindResultDto> bindApply(@RequestBody BindApplyDto bindApply);

    @PostMapping("confirm")
    RestResult<BindResultDto> bindConfirm(@RequestBody BindConfirmDto bindConfirm);
}

package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;

import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadRequestDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanResultDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileResultDto;
import com.jinghang.capital.api.dto.file.FileUploadDto;
import com.jinghang.capital.api.dto.file.FileUploadResultDto;
import com.jinghang.capital.api.dto.file.PreviewDto;
import com.jinghang.capital.api.dto.file.PreviewResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
public interface LoanFileService {
    @PostMapping("download")
    RestResult<FileDownloadResultDto> download(@RequestBody FileDownloadDto fileDownloadDto);

    /**
     * 文件上传
     *
     * @param fileUploadDto
     * @return
     */
    @PostMapping("upload")
    RestResult<FileUploadResultDto> upload(@RequestBody FileUploadDto fileUploadDto);

    @PostMapping("daily")
    RestResult<Void> dailyProcess(@RequestBody FileDailyProcessDto processDto);

    /**
     * 文件上传
     * @param fileDownloadRequestDto
     * @return
     */
    @PostMapping("downloadRequest")
    RestResult<Void> downloadRequest(@RequestBody FileDownloadRequestDto fileDownloadRequestDto);

    /**
     * 协议预览
     *
     * @param previewDto
     * @return
     */
    @PostMapping("preview")
    RestResult<PreviewResultDto> preview(@RequestBody PreviewDto previewDto);

    /**
     * 同步还款计划文件
     */
    @PostMapping("synRepayPlanFile")
    RestResult<FileSynRepayPlanResultDto> synRepayPlanFile(@RequestBody FileSynRepayPlanDto fileSynRepayPlanDto);

    /**
     * 同步融担费
     */
    @PostMapping("syncDueFile")
    RestResult<FileSyncDueFileResultDto> syncDueFile(@RequestBody FileSyncDueFileDto fileSyncDueFileDto);

}

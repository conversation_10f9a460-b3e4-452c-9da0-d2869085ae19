package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.fund.FundAccountFlowQueryDto;
import com.jinghang.capital.api.dto.fund.FundChangeQueryDto;
import com.jinghang.capital.api.dto.fund.FundChangeQueryResultDto;
import com.jinghang.capital.api.dto.fund.FundDefrayDeductDto;
import com.jinghang.capital.api.dto.fund.FundDefraySumDto;
import com.jinghang.capital.api.dto.fund.FundFlowQueryDto;
import com.jinghang.capital.api.dto.fund.FundFlowQueryDtoResultDto;
import com.jinghang.capital.api.dto.fund.FundLoanDetailQueryDto;
import com.jinghang.capital.api.dto.fund.FundQueryDto;
import com.jinghang.capital.api.dto.fund.FundQueryResultDto;
import com.jinghang.capital.api.dto.fund.FundTrustAccountQueryDto;
import com.jinghang.capital.api.dto.fund.FundTrustAccountQueryResultDto;
import com.jinghang.capital.api.dto.fund.FundTrustChangeDto;
import com.jinghang.capital.api.dto.fund.FundTrustQueryDto;
import com.jinghang.capital.api.dto.fund.FundTrustQueryResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> jingtao
 * @Classname FundService
 * @Description 信托资金服务
 * @Date 2024/9/7
 */
public interface FundService {

    /**
     * 信托计划查询
     */
    @PostMapping("trustQuery")
    RestResult<FundTrustQueryResultDto> trustQuery(@RequestBody FundTrustQueryDto fundTrustQueryDto);

    /**
     * 账户余额查询
     */
    @PostMapping("trustAccount")
    RestResult<FundTrustAccountQueryResultDto> trustAccountQuery(@RequestBody FundTrustAccountQueryDto fundTrustAccountQueryDto);

    /**
     * 资金明细查询
     */
    @PostMapping("accountFlow")
    RestResult<Void> accountFlowQuery(@RequestBody FundAccountFlowQueryDto fundAccountFlowQueryDto);

    /**
     * 放款明细查询
     */
    @PostMapping("loanDetailQuery")
    RestResult<Void> loanDetailQuery(@RequestBody FundLoanDetailQueryDto fundLoanDetailQueryDto);

    /**
     * 用户流水查询
     */
    @PostMapping("fundQuery")
    RestResult<FundQueryResultDto> fundQuery(@RequestBody FundQueryDto fundQueryDto);

    /**
     * 流水查询
     */
    @PostMapping("fundFlowQuery")
    RestResult<FundFlowQueryDtoResultDto> fundFlowQuery(@RequestBody FundFlowQueryDto fundFlowQueryDto);

    /**
     * 信托规模变更
     */
    @PostMapping("fundChange")
    RestResult<Void> trustChange(@RequestBody FundTrustChangeDto fundTrustChangeDto);


    /**
     * 信托规模变更
     */
    @PostMapping("fundChangeQuery")
    RestResult<FundChangeQueryResultDto> fundChangeQuery(@RequestBody FundChangeQueryDto fundChangeQueryDto);

    /**
     * 服务信托 代付金额汇总确认
     */
    @PostMapping("fundDefraySum")
    RestResult<Void> fundDefraySum(@RequestBody FundDefraySumDto fundDefraySumDto);

    /**
     * 服务信托 代付金额划扣
     */
    @PostMapping("fundDefrayDeduct")
    RestResult<Void> fundDefrayDeduct(@RequestBody FundDefrayDeductDto fundDefrayDeductDto);


}

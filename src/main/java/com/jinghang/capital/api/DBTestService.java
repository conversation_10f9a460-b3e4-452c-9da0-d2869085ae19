package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.test.DBCrudDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 授信、放款、还款核心表 CRUD 冒烟测试
 */
public interface DBTestService {

    /**
     * 授信、放款、还款核心表 CRUD 冒烟测试
     *
     * @param dto  请求入参
     * @return 内容
     */
    @PostMapping("/db/crud")
    RestResult<String> test(@RequestBody DBCrudDto dto);


}

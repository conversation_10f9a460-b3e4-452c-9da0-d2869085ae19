package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.BusinessChronosProcessDto;
import com.jinghang.capital.api.dto.BusinessChronosProcessResultDto;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.api.dto.loan.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface LoanService {

    @PostMapping("apply")
    RestResult<LoanResultDto> loan(@RequestBody LoanApplyDto loanApply);

    /**
     * 查询资方额度，利率
     * @param loanApply
     * @return
     */
    @PostMapping("limit")
    RestResult<LoanLimitResultDto> queryLimit(@RequestBody LoanLimitQueryDto loanApply);

    @PostMapping("query")
    RestResult<LoanResultDto> queryResult(@RequestBody LoanQueryDto loanQuery);

    @PostMapping("lpr")
    RestResult<LoanLprResultDto> lprResult(@RequestBody LoanLprQueryDto lprQuery);

    @PostMapping("trial")
    RestResult<LoanTrialResultDto> loanTrial(@RequestBody LoanTrialQueryDto loanTrial);

    /**
     * 放款结果通知
     *
     * @param loanNoticeDto
     * @return
     */
    @PostMapping("notice")
    RestResult<LoanNoticeResultDto> loanNotice(@RequestBody LoanNoticeDto loanNoticeDto);

    /**
     * 查询资方借款合同编号
     *
     * @param processDto 日期相关处理
     * @return
     */
    @PostMapping("queryContractNo")
    RestResult<BusinessChronosProcessResultDto> loanContractNoQuery(@RequestBody BusinessChronosProcessDto processDto);



    /**
     * 放款结果回调
     * @param bankResultBackDto
     * @return
     */
    @PostMapping("loanResultBack")
    RestResult<BankResultBackDto> loanResultBack(@RequestBody BankResultBackDto bankResultBackDto);


}

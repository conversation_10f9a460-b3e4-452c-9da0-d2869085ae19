package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.plan.BankPlanQueryDto;
import com.jinghang.capital.api.dto.plan.BankPlanQueryRltDto;
import com.jinghang.capital.api.dto.plan.PlanOverdueDto;
import com.jinghang.capital.api.dto.plan.PlanSyncDto;
import com.jinghang.capital.api.dto.plan.PlanSyncRltDto;
import com.jinghang.capital.api.dto.repay.PlanDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
public interface PlanService {
    /**
     * 渠道方上传,fin-core转存oss,记录ReconciliationFile
     *
     * @param planSyncDto
     * @return
     */
    @PostMapping("sync")
    RestResult<PlanSyncRltDto> sync(@RequestBody PlanSyncDto planSyncDto);

    /**
     * batch解析转存oss后 调用此接口推送资方
     *
     * @param id
     * @return
     */
    @PostMapping("push")
    RestResult<PlanSyncRltDto> push(@RequestParam("id") String id);


    /**
     * 推送有服务费的还款计划.
     *
     * @return 结果
     */
    @PostMapping("detailPush")
    RestResult<Void> detailPush(@RequestBody PlanDto planDetailDto);

    /**
     * 逾期罚息计算
     */
    @PostMapping("overdue")
    RestResult<Void> planOverdue(@RequestBody PlanOverdueDto trailDto);


    /**
     * 查询对资还款计划
     */
    @PostMapping("bankPlanQuery")
    RestResult<BankPlanQueryRltDto> bankPlanQuery(@RequestBody BankPlanQueryDto trailDto);
}

package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
public interface VoucherFileService {

    @PostMapping("download")
    RestResult<FileDownloadResultDto> download(@RequestBody FileDownloadDto fileDownloadDto);

    /**
     * 文件申请结果查询
     * @param processDto
     * @return
     */
    @PostMapping("fileApplyQuery")
    RestResult<Void> fileApplyQuery(@RequestBody FileDailyProcessDto processDto);


    /**
     * 批量下载文件
     *
     * @param processDto
     * @return
     */
    @PostMapping("batchDownload")
    RestResult<Void> batchDownload(@RequestBody FileDailyProcessDto processDto);
}

package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface QuotaService {

    /**
     * 资产正向调额结果通知
     * @param bankResultBackDto
     * @return
     */
    @PostMapping("upQuotaBack")
    RestResult<BankResultBackDto> upQuotaBack(@RequestBody BankResultBackDto bankResultBackDto);
}

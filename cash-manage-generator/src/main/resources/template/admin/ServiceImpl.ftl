/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package ${package}.service.impl;

import ${package}.domain.${className};
<#if columns??>
    <#list columns as column>
        <#if column.columnKey = 'UNI'>
            <#if column_index = 1>
import me.zhengjie.exception.EntityExistException;
            </#if>
        </#if>
    </#list>
</#if>
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${package}.service.${className}Service;
import ${package}.domain.dto.${className}QueryCriteria;
import ${package}.mapper.${className}Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jinghang.cash.utils.PageUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务实现
* <AUTHOR>
* @date ${date}
**/
@Service
@RequiredArgsConstructor
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

    private final ${className}Mapper ${changeClassName}Mapper;

    @Override
    public PageResult<${className}> queryAllPage(${className}QueryCriteria criteria){
        Page<${className}> page = new Page<>(criteria.getPage(), criteria.getSize());
        LambdaQueryWrapper<${className}> wrapper = new LambdaQueryWrapper<${className}>();
        // TODO: 根据实际需要添加查询条件
        page = ${changeClassName}Mapper.selectPage(page, wrapper);
        return PageUtil.toPage(page.getRecords(), page.getTotal());
    }

    @Override
    public List<${className}> queryAll(${className}QueryCriteria criteria){
        return ${changeClassName}Mapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(${className} resources) {
        ${changeClassName}Mapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(${className} resources) {
        ${className} ${changeClassName} = getById(resources.get${pkCapitalColName}());
        ${changeClassName}.copy(resources);
        ${changeClassName}Mapper.updateById(${changeClassName});
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<${pkColumnType}> ids) {
        ${changeClassName}Mapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<${className}> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (${className} ${changeClassName} : all) {
            Map<String,Object> map = new LinkedHashMap<>();
        <#list columns as column>
            <#if column.columnKey != 'PRI'>
            <#if column.remark != ''>
            map.put("${column.remark}", ${changeClassName}.get${column.capitalColumnName}());
            <#else>
            map.put(" ${column.changeColumnName}",  ${changeClassName}.get${column.capitalColumnName}());
            </#if>
            </#if>
        </#list>
            list.add(map);
        }
        com.jinghang.cash.utils.FileUtil.downloadExcel(list, response);
    }
}
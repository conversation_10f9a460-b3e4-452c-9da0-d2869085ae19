/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package ${package}.service;

import ${package}.domain.${className};
import ${package}.domain.dto.${className}Dto;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务接口
* <AUTHOR>
* @date ${date}
**/
public interface ${className}Service extends IService<${className}> {

    /**
    * 查询数据分页
    * @param dto 条件
    * @return PageResult
    */
    PageResult<${className}> queryAllPage(${className}Dto dto);

    /**
    * 查询所有数据不分页
    * @param dto 条件参数
    * @return List<${className}>
    */
    List<${className}> queryAll(${className}Dto dto);

    /**
    * 创建
    * @param resources /
    */
    void create(${className} resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(${className} resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(List<${pkColumnType}> ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<${className}> all, HttpServletResponse response) throws IOException;
}